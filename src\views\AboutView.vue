<template>
  <div class="about">
    <!-- 英雄区域 -->
    <section class="about-hero">
      <div class="hero-background">
        <div class="floating-elements">
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 0s;">
            <div class="card-icon">🏢</div>
          </div>
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 1s;">
            <div class="card-icon">🎯</div>
          </div>
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 2s;">
            <div class="card-icon">🚀</div>
          </div>
        </div>
      </div>

      <div class="hero-content">
        <div class="hero-badge glass tech-glow scan-effect">
          <span class="badge-icon">🏆</span>
          <span class="badge-text gradient-text-cyber">关于我们</span>
        </div>

        <h1 class="hero-title">
          <span class="title-main gradient-text">科技驱动未来</span>
          <span class="title-sub gradient-text-gold">创新引领生活</span>
        </h1>

        <p class="hero-description">
          宁波圣芽儿商贸科技有限公司，致力于运用前沿科技重新定义本地生活服务。
          我们相信，科技的力量能够让生活更加美好，让服务更加智能。
        </p>
      </div>
    </section>

    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container">
        <div class="intro-content glass cyber-border">
          <div class="intro-header">
            <h2 class="section-title gradient-text">企业简介</h2>
            <div class="title-decoration"></div>
          </div>

          <div class="intro-text">
            <p>
              宁波圣芽儿商贸科技有限公司成立于2024年，是一家专注于本地生活服务数字化转型的高新技术企业。
              我们运用人工智能、大数据分析、云计算等前沿技术，为宁波及周边地区的消费者和商家提供智慧化的生活服务解决方案。
            </p>
            <p>
              公司秉承"科技让生活更美好"的理念，致力于构建智慧生活生态圈，通过技术创新推动传统服务业的数字化升级，
              为用户创造更便捷、更高效、更个性化的服务体验。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心价值观 -->
    <section class="core-values">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title gradient-text">核心价值观</h2>
          <p class="section-subtitle">指引我们前进的价值理念</p>
        </div>

        <div class="values-grid">
          <div class="value-card glass tech-glow" v-for="(value, index) in coreValues" :key="index">
            <div class="value-icon">{{ value.icon }}</div>
            <h3 class="value-title gradient-text-cyber">{{ value.title }}</h3>
            <p class="value-description">{{ value.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="company-timeline">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title gradient-text">发展历程</h2>
          <p class="section-subtitle">见证我们的成长足迹</p>
        </div>

        <div class="timeline">
          <div class="timeline-item glass" v-for="(milestone, index) in timeline" :key="index">
            <div class="timeline-date gradient-text-gold">{{ milestone.date }}</div>
            <div class="timeline-content">
              <h3 class="timeline-title">{{ milestone.title }}</h3>
              <p class="timeline-description">{{ milestone.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'AboutView',
  setup() {
    const coreValues = ref([
      {
        icon: '🎯',
        title: '用户至上',
        description: '始终将用户需求放在首位，用心倾听，用技术回应，创造超越期待的服务体验'
      },
      {
        icon: '🚀',
        title: '创新驱动',
        description: '拥抱变化，勇于创新，运用前沿科技不断突破传统服务模式的边界'
      },
      {
        icon: '🤝',
        title: '合作共赢',
        description: '与合作伙伴携手共进，构建开放共享的生态体系，实现多方价值最大化'
      },
      {
        icon: '⚡',
        title: '高效执行',
        description: '快速响应市场变化，高效执行战略决策，以敏捷的行动力赢得竞争优势'
      },
      {
        icon: '🌱',
        title: '持续成长',
        description: '保持学习心态，持续优化改进，与时俱进地提升服务质量和技术水平'
      },
      {
        icon: '🛡️',
        title: '诚信负责',
        description: '坚持诚信经营，承担社会责任，以可靠的品质和服务赢得信任与尊重'
      }
    ])

    const timeline = ref([
      {
        date: '2024.01',
        title: '公司成立',
        description: '宁波圣芽儿商贸科技有限公司正式成立，开启数字化本地生活服务新征程'
      },
      {
        date: '2024.03',
        title: '技术团队组建',
        description: '汇聚行业精英，组建专业的技术研发团队，奠定技术创新基础'
      },
      {
        date: '2024.06',
        title: '产品原型发布',
        description: '完成核心产品原型开发，开始小范围用户测试和市场验证'
      },
      {
        date: '2024.09',
        title: '正式运营',
        description: '产品正式上线运营，开始为宁波地区用户提供智慧生活服务'
      },
      {
        date: '2024.12',
        title: '业务拓展',
        description: '服务范围扩展至周边城市，用户规模快速增长，获得市场认可'
      }
    ])

    return {
      coreValues,
      timeline
    }
  }
})
</script>

<style scoped>
.about {
  padding-top: 80px;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0e4b99 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* 英雄区域 */
.about-hero {
  min-height: 60vh;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 120px 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  z-index: 2;
}

.floating-card .card-icon {
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
}

.floating-card:nth-child(1) {
  top: 20%;
  left: 10%;
}

.floating-card:nth-child(2) {
  top: 60%;
  right: 15%;
}

.floating-card:nth-child(3) {
  top: 40%;
  left: 80%;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  text-align: center;
  position: relative;
  z-index: 3;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 32px;
  color: #ffffff;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
}

.hero-badge .badge-icon {
  filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.8));
}

.hero-title {
  margin-bottom: 32px;
}

.title-main {
  display: block;
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -2px;
  margin-bottom: 16px;
}

.title-sub {
  display: block;
  font-size: 2rem;
  font-weight: 300;
  color: #ffffff;
  opacity: 0.9;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.hero-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.9;
  line-height: 1.7;
  margin-bottom: 48px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* 公司介绍 */
.company-intro {
  padding: 120px 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.intro-content {
  padding: 60px;
  border-radius: 24px;
  position: relative;
}

.intro-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: -1px;
}

.title-decoration {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #00d4ff, #7c3aed);
  margin: 0 auto;
  border-radius: 2px;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.8);
}

.intro-text p {
  margin-bottom: 24px;
}

/* 核心价值观 */
.core-values {
  padding: 120px 0;
  background: rgba(255, 255, 255, 0.02);
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 16px;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.value-card {
  padding: 40px 32px;
  border-radius: 24px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.value-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 24px;
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.6));
}

.value-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.value-description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* 发展历程 */
.company-timeline {
  padding: 120px 0;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #00d4ff, #7c3aed);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 60px;
  padding: 30px;
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.timeline-item:nth-child(odd) {
  margin-right: 50%;
  margin-left: 0;
}

.timeline-item:nth-child(even) {
  margin-left: 50%;
  margin-right: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #00d4ff, #7c3aed);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
}

.timeline-item:nth-child(odd)::before {
  right: -60px;
}

.timeline-item:nth-child(even)::before {
  left: -60px;
}

.timeline-date {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.timeline-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
}

.timeline-description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .hero-content {
    padding: 0 20px;
  }

  .title-main {
    font-size: 2.5rem;
  }

  .title-sub {
    font-size: 1.4rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .intro-content {
    padding: 40px 30px;
  }

  .timeline::before {
    left: 20px;
  }

  .timeline-item {
    margin-left: 50px !important;
    margin-right: 0 !important;
  }

  .timeline-item::before {
    left: -60px !important;
  }
}
</style>
