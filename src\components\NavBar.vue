<template>
  <nav class="navbar glass">
    <div class="nav-container">
      <div class="nav-brand">
        <div class="brand-icon tech-glow">⚡</div>
        <span class="brand-text gradient-text-cyber">宁波圣芽儿</span>
        <div class="brand-subtitle">TECH</div>
      </div>
      
      <div class="nav-links" :class="{ 'active': isMenuOpen }">
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('home')">首页</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('about')">关于我们</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('products')">产品服务</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('culture')">企业文化</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('contact')">联系我们</a>
        <router-link to="/careers" class="nav-link careers-link">招贤纳士</router-link>
      </div>
      
      <button class="mobile-menu-btn" @click="toggleMenu">
        <span class="hamburger-line" :class="{ 'active': isMenuOpen }"></span>
        <span class="hamburger-line" :class="{ 'active': isMenuOpen }"></span>
        <span class="hamburger-line" :class="{ 'active': isMenuOpen }"></span>
      </button>
    </div>
  </nav>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'NavBar',
  setup() {
    const router = useRouter()
    const isMenuOpen = ref(false)
    const isScrolled = ref(false)

    const toggleMenu = () => {
      isMenuOpen.value = !isMenuOpen.value
    }

    const scrollToSection = (sectionId: string) => {
      // 关闭移动端菜单
      isMenuOpen.value = false
      
      // 如果当前不在首页，先跳转到首页
      if (router.currentRoute.value.path !== '/') {
        router.push('/').then(() => {
          // 等待路由跳转完成后再滚动
          setTimeout(() => {
            const element = document.getElementById(sectionId)
            if (element) {
              const offsetTop = element.offsetTop - 80
              window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
              })
            }
          }, 100)
        })
      } else {
        // 如果已经在首页，直接滚动
        const element = document.getElementById(sectionId)
        if (element) {
          const offsetTop = element.offsetTop - 80
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          })
        }
      }
    }

    const handleScroll = () => {
      isScrolled.value = window.scrollY > 50
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      isMenuOpen,
      isScrolled,
      toggleMenu,
      scrollToSection
    }
  }
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 16px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  animation: scan-line 4s ease-in-out infinite;
  pointer-events: none;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffffff;
  text-decoration: none;
  position: relative;
}

.brand-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 8px rgba(0, 212, 255, 0.5));
  animation: pulse-glow 2s ease-in-out infinite;
}

.brand-text {
  font-weight: 800;
  letter-spacing: -0.5px;
}

.brand-subtitle {
  font-size: 0.7rem;
  color: #00d4ff;
  font-weight: 600;
  letter-spacing: 2px;
  opacity: 0.8;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-link:hover {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link.router-link-active {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
}

.careers-link {
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  color: white !important;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
  border: 1px solid rgba(0, 212, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.careers-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.careers-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 212, 255, 0.4);
  background: linear-gradient(135deg, #7c3aed 0%, #00d4ff 100%);
}

.careers-link:hover::after {
  left: 100%;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: #8B5A3C;
  margin: 2px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px;
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .nav-container {
    padding: 0 20px;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-links {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: rgba(250, 247, 240, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    flex-direction: column;
    padding: 32px 20px;
    gap: 20px;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(139, 90, 60, 0.1);
  }
  
  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-link {
    width: 100%;
    text-align: center;
    padding: 12px 20px;
    font-size: 16px;
  }
  
  .careers-link {
    margin-top: 16px;
    padding: 14px 24px;
  }
}

@media screen and (max-width: 480px) {
  .nav-container {
    padding: 0 16px;
  }
  
  .brand-text {
    font-size: 1rem;
  }
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(212, 165, 116, 0.1);
}
</style>