<template>
  <nav class="navbar glass">
    <div class="nav-container">
      <div class="nav-brand">
        <div class="brand-icon">🌟</div>
        <span class="brand-text">宁波圣芽儿</span>
      </div>
      
      <div class="nav-links" :class="{ 'active': isMenuOpen }">
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('home')">首页</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('about')">关于我们</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('products')">产品服务</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('culture')">企业文化</a>
        <a href="javascript:void(0)" class="nav-link" @click="scrollToSection('contact')">联系我们</a>
        <router-link to="/careers" class="nav-link careers-link">招贤纳士</router-link>
      </div>
      
      <button class="mobile-menu-btn" @click="toggleMenu">
        <span class="hamburger-line" :class="{ 'active': isMenuOpen }"></span>
        <span class="hamburger-line" :class="{ 'active': isMenuOpen }"></span>
        <span class="hamburger-line" :class="{ 'active': isMenuOpen }"></span>
      </button>
    </div>
  </nav>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'NavBar',
  setup() {
    const router = useRouter()
    const isMenuOpen = ref(false)
    const isScrolled = ref(false)

    const toggleMenu = () => {
      isMenuOpen.value = !isMenuOpen.value
    }

    const scrollToSection = (sectionId: string) => {
      // 关闭移动端菜单
      isMenuOpen.value = false
      
      // 如果当前不在首页，先跳转到首页
      if (router.currentRoute.value.path !== '/') {
        router.push('/').then(() => {
          // 等待路由跳转完成后再滚动
          setTimeout(() => {
            const element = document.getElementById(sectionId)
            if (element) {
              const offsetTop = element.offsetTop - 80
              window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
              })
            }
          }, 100)
        })
      } else {
        // 如果已经在首页，直接滚动
        const element = document.getElementById(sectionId)
        if (element) {
          const offsetTop = element.offsetTop - 80
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          })
        }
      }
    }

    const handleScroll = () => {
      isScrolled.value = window.scrollY > 50
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      isMenuOpen,
      isScrolled,
      toggleMenu,
      scrollToSection
    }
  }
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 16px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.2rem;
  font-weight: 700;
  color: #8B5A3C;
  text-decoration: none;
}

.brand-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 8px rgba(212, 165, 116, 0.3));
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  color: #8B5A3C;
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
}

.nav-link:hover {
  color: #D4A574;
  background: rgba(212, 165, 116, 0.1);
  transform: translateY(-1px);
}

.nav-link.router-link-active {
  color: #D4A574;
  background: rgba(212, 165, 116, 0.15);
}

.careers-link {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white !important;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
}

.careers-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(212, 165, 116, 0.4);
  background: linear-gradient(135deg, #8B5A3C 0%, #D4A574 100%);
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: #8B5A3C;
  margin: 2px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px;
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .nav-container {
    padding: 0 20px;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-links {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: rgba(250, 247, 240, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    flex-direction: column;
    padding: 32px 20px;
    gap: 20px;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(139, 90, 60, 0.1);
  }
  
  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-link {
    width: 100%;
    text-align: center;
    padding: 12px 20px;
    font-size: 16px;
  }
  
  .careers-link {
    margin-top: 16px;
    padding: 14px 24px;
  }
}

@media screen and (max-width: 480px) {
  .nav-container {
    padding: 0 16px;
  }
  
  .brand-text {
    font-size: 1rem;
  }
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(212, 165, 116, 0.1);
}
</style>