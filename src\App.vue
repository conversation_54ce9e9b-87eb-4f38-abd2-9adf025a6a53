<template>
  <div id="app">
    <NavBar />
    <router-view />
    <Footer />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import NavBar from '@/components/NavBar.vue'
import Footer from '@/components/Footer.vue'

export default defineComponent({
  name: 'App',
  components: {
    NavBar,
    Footer
  }
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #8B5A3C;
}

body {
  overflow-x: hidden;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f1e8 50%, #ede7d3 100%);
}

/* 全局毛玻璃效果 */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 50%, #D4A574 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-gold {
  background: linear-gradient(135deg, #D4A574 0%, #B8860B 50%, #DAA520 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 动画效果 */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) rotate(0deg); 
  }
  25% { 
    transform: translateY(-20px) translateX(10px) rotate(2deg); 
  }
  50% { 
    transform: translateY(0px) translateX(20px) rotate(0deg); 
  }
  75% { 
    transform: translateY(-10px) translateX(10px) rotate(-1deg); 
  }
}

@keyframes pulse-soft {
  0%, 100% { 
    transform: scale(1); 
    opacity: 1; 
  }
  50% { 
    transform: scale(1.05); 
    opacity: 0.8; 
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 4s ease-in-out infinite;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #D4A574 0%, #8B5A3C 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #8B5A3C 0%, #D4A574 100%);
}

/* 全局按钮样式 */
button {
  font-family: inherit;
  outline: none;
  border: none;
  cursor: pointer;
}

/* 全局链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 隐藏元素 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 全局过渡效果 */
* {
  transition: all 0.3s ease;
}

/* 禁用过渡的元素 */
.no-transition,
.no-transition * {
  transition: none !important;
}
</style>