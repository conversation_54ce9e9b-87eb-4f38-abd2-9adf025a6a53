  font-size: 14px;
}

.upload-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(212, 165, 116, 0.2);
  border-top: 2px solid #D4A574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.upload-success {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #4CAF50;
  font-size: 14px;
}

.success-icon {
  font-weight: bold;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 16px;
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: rgba(139, 90, 60, 0.1);
  color: #8B5A3C;
}

.btn-cancel:hover {
  background: rgba(139, 90, 60, 0.2);
}

.btn-submit {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
}

.btn-submit:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
}

.btn-submit:disabled,
.btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media screen and (max-width: 768px) {
  .container {
    padding: 0 20px;
  }
  
  .hero-content {
    padding: 0 20px;
  }
  
  .title-main {
    font-size: 2.5rem;
  }
  
  .title-sub {
    font-size: 1.4rem;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .position-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .position-meta {
    gap: 8px;
  }
  
  .modal-content {
    margin: 20px;
    padding: 24px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@keyframes animate-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}
</style></search>
<template>
  <div class="careers">
    <!-- 页面头部 -->
    <section class="careers-hero">
      <div class="hero-background">
        <div class="floating-elements">
          <div class="floating-card glass animate-float" style="animation-delay: 0s;">
            <div class="card-icon">💼</div>
          </div>
          <div class="floating-card glass animate-float" style="animation-delay: 1s;">
            <div class="card-icon">🚀</div>
          </div>
          <div class="floating-card glass animate-float" style="animation-delay: 2s;">
            <div class="card-icon">⭐</div>
          </div>
        </div>
      </div>
      
      <div class="hero-content">
        <div class="hero-badge glass">
          <span class="badge-icon">🎯</span>
          <span class="badge-text">加入我们</span>
        </div>
        
        <h1 class="hero-title">
          <span class="title-main gradient-text">招贤纳士</span>
          <span class="title-sub">与优秀的人一起成长</span>
        </h1>
        
        <p class="hero-description">
          我们正在寻找有才华、有激情的伙伴加入我们的团队。
          在这里，你将与行业精英共事，参与创新项目，实现个人价值与公司发展的双赢。
        </p>
      </div>
    </section>

    <!-- 为什么选择我们 -->
    <section class="why-join">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title gradient-text">为什么选择我们</h2>
          <p class="section-subtitle">优秀的团队 · 广阔的平台 · 无限的可能</p>
        </div>
        
        <div class="benefits-grid">
          <div class="benefit-card glass" v-for="(benefit, index) in benefits" :key="index">
            <div class="benefit-icon">{{ benefit.icon }}</div>
            <h3 class="benefit-title">{{ benefit.title }}</h3>
            <p class="benefit-description">{{ benefit.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 职位列表 -->
    <section class="job-positions">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title gradient-text">热招职位</h2>
          <p class="section-subtitle">多个岗位虚位以待，期待你的加入</p>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载职位信息...</p>
        </div>
        
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container glass">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <p class="error-subtitle">正在展示默认职位数据</p>
        </div>
        
        <!-- 职位列表 -->
        <div v-else class="positions-list">
          <div class="position-card glass" v-for="position in positions" :key="position.id">
            <div class="position-header">
              <div class="position-info">
                <h3 class="position-title">{{ position.title }}</h3>
                <div class="position-meta">
                  <span class="position-department">{{ position.department }}</span>
                  <span class="position-type">{{ position.employmentTypeDesc }}</span>
                  <span class="position-salary">{{ position.salaryRange }}</span>
                </div>
              </div>
              <div class="position-status">热招</div>
            </div>
            
            <div class="position-requirements">
              <h4>职位要求：</h4>
              <ul>
                <li v-for="requirement in parseRequirements(position.requirements)" :key="requirement">
                  {{ requirement }}
                </li>
              </ul>
            </div>
            
            <div class="position-actions">
              <button class="btn-apply glass" @click="showResumeForm(position)">
                立即投递
              </button>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!loading && !error && positions.length === 0" class="empty-state glass">
          <div class="empty-icon">📋</div>
          <p class="empty-text">暂无招聘职位</p>
          <p class="empty-subtitle">请稍后再试</p>
        </div>
      </div>
    </section>

    <!-- 简历投递表单 -->
    <div class="resume-modal" v-if="showModal" @click="closeModal">
      <div class="modal-content glass" @click.stop>
        <div class="modal-header">
          <h3>投递简历</h3>
          <button class="close-btn" @click="closeModal">×</button>
        </div>
        
        <form class="resume-form" @submit.prevent="submitResume">
          <div class="form-group">
            <label>姓名 *</label>
            <input type="text" v-model="resumeForm.name" required>
          </div>
          
          <div class="form-group">
            <label>手机号 *</label>
            <input type="tel" v-model="resumeForm.phone" required>
          </div>
          
          <div class="form-group">
            <label>邮箱 *</label>
            <input type="email" v-model="resumeForm.email" required>
          </div>
          
          <div class="form-group">
            <label>年龄</label>
            <input type="number" v-model="resumeForm.age" min="18" max="65" placeholder="请输入年龄">
          </div>
          
          <div class="form-group">
            <label>应聘职位</label>
            <input type="text" v-model="resumeForm.position" readonly>
          </div>
          
          <div class="form-group">
            <label>工作经验 *</label>
            <textarea v-model="resumeForm.experience" rows="3" placeholder="请详细描述您的工作经验，如：5年Java开发经验，熟悉Spring框架，有微服务开发经验" required></textarea>
          </div>
          
          <div class="form-group">
            <label>求职信</label>
            <textarea v-model="resumeForm.coverLetter" rows="4" placeholder="我对这个岗位很感兴趣，希望能够加入贵公司..."></textarea>
          </div>
          
          <div class="form-group">
            <label>简历附件</label>
            <input type="file" @change="handleFileUpload" accept=".pdf,.doc,.docx" :disabled="uploading">
            <div v-if="uploading" class="upload-status">
              <div class="upload-spinner"></div>
              <span>正在上传文件...</span>
            </div>
            <div v-if="resumeForm.resumeFile" class="upload-success">
              <span class="success-icon">✓</span>
              <span>文件上传成功</span>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn-cancel" @click="closeModal" :disabled="submitting || uploading">取消</button>
            <button type="submit" class="btn-submit" :disabled="submitting || uploading">
              {{ submitting ? '提交中...' : '提交简历' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'

interface Job {
  id: number
  title: string
  department: string
  employmentType: string
  salaryRange: string
  requirements: string
  employmentTypeDesc: string
}

interface ApiResponse {
  code: number
  message: string
  data: Job[]
  timestamp: number
  pageNum: number
  pageSize: number
  total: number
  pages: number
  hasNext: boolean
  hasPrevious: boolean
  success: boolean
}

export default defineComponent({
  name: 'CareersView',
  setup() {
    const showModal = ref(false)
    const loading = ref(false)
    const error = ref('')
    const positions = ref<Job[]>([])
    const submitting = ref(false)
    const uploading = ref(false)
    
    const resumeForm = ref({
      name: '',
      phone: '',
      email: '',
      age: null as number | null,
      jobId: null as number | null,
      position: '',
      experience: '',
      coverLetter: '',
      resumeFile: '',
      resume: null as File | null
    })

    const benefits = ref([
      {
        icon: '💰',
        title: '具有竞争力的薪酬',
        description: '行业领先的薪资水平，完善的绩效奖励机制，让你的付出得到应有的回报'
      },
      {
        icon: '🚀',
        title: '广阔的发展空间',
        description: '扁平化管理结构，快速的晋升通道，丰富的培训机会，助力你的职业发展'
      },
      {
        icon: '🏖️',
        title: '灵活的工作方式',
        description: '弹性工作时间，远程办公支持，带薪年假，让工作与生活完美平衡'
      },
      {
        icon: '🎓',
        title: '持续学习成长',
        description: '技术分享会，外部培训支持，图书津贴，打造学习型组织文化'
      },
      {
        icon: '🏥',
        title: '完善的福利保障',
        description: '五险一金，补充医疗保险，定期体检，节日福利，全方位保障你的生活'
      },
      {
        icon: '🎉',
        title: '温馨的团队氛围',
        description: '年轻活力的团队，开放包容的文化，定期团建活动，让工作充满乐趣'
      }
    ])

    // 获取职位数据
    const fetchJobs = async () => {
      loading.value = true
      error.value = ''
      
      try {
        const response = await fetch('http://localhost:1680/api/jobs')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const result: ApiResponse = await response.json()
        
        if (result.success && result.code === 200) {
          positions.value = result.data
        } else {
          throw new Error(result.message || '获取职位数据失败')
        }
      } catch (err) {
        console.error('获取职位数据失败:', err)
        error.value = err instanceof Error ? err.message : '网络请求失败'
        
        // 如果API请求失败，使用默认数据
        positions.value = [
          {
            id: 1,
            title: '前端开发工程师',
            department: '技术部',
            employmentType: 'full',
            salaryRange: '12K-20K',
            requirements: '1. 3年以上前端开发经验，熟练掌握Vue.js、React等主流框架\n2. 精通HTML5、CSS3、JavaScript，了解ES6+新特性\n3. 熟悉前端工程化工具，如Webpack、Vite等\n4. 有移动端开发经验，了解响应式设计\n5. 良好的代码规范和团队协作能力',
            employmentTypeDesc: '全职'
          },
          {
            id: 2,
            title: '后端开发工程师',
            department: '技术部',
            employmentType: 'full',
            salaryRange: '15K-25K',
            requirements: '1. 3年以上后端开发经验，熟练掌握Java、Python或Node.js\n2. 熟悉Spring Boot、Django等主流框架\n3. 熟练使用MySQL、Redis等数据库\n4. 了解微服务架构，有分布式系统开发经验\n5. 良好的系统设计能力和问题解决能力',
            employmentTypeDesc: '全职'
          }
        ]
      } finally {
        loading.value = false
      }
    }

    // 解析职位要求字符串为数组
    const parseRequirements = (requirements: string): string[] => {
      if (!requirements) return []
      return requirements.split('\n').filter(req => req.trim() !== '')
    }

    const showResumeForm = (position: Job) => {
      resumeForm.value.position = position.title
      resumeForm.value.jobId = position.id
      showModal.value = true
    }

    const closeModal = () => {
      showModal.value = false
      resetForm()
    }

    const resetForm = () => {
      resumeForm.value = {
        name: '',
        phone: '',
        email: '',
        age: null,
        jobId: null,
        position: '',
        experience: '',
        coverLetter: '',
        resumeFile: '',
        resume: null
      }
    }

    // 上传文件到服务器
    const uploadFile = async (file: File): Promise<string> => {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('http://localhost:1680/api/files/resume', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`文件上传失败: ${response.status}`)
      }

      const result = await response.json()
      
      if (result.success && result.code === 200) {
        // 确保返回的是字符串URL，格式如："/api/files/resume/resume_20250731154545_65a0fe3f.pdf"
        return typeof result.data === 'string' ? result.data : result.data.url || result.data.path || result.data
      } else {
        throw new Error(result.message || '文件上传失败')
      }
    }

    const handleFileUpload = async (event: Event) => {
      const target = event.target as HTMLInputElement
      if (target.files && target.files[0]) {
        const file = target.files[0]
        
        // 验证文件类型
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
        if (!allowedTypes.includes(file.type)) {
          alert('请上传PDF、DOC或DOCX格式的文件')
          target.value = ''
          return
        }
        
        // 验证文件大小 (10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert('文件大小不能超过10MB')
          target.value = ''
          return
        }
        
        uploading.value = true
        resumeForm.value.resume = file
        
        try {
          const fileUrl = await uploadFile(file)
          resumeForm.value.resumeFile = fileUrl
        } catch (err) {
          console.error('文件上传失败:', err)
          alert(err instanceof Error ? err.message : '文件上传失败，请重试')
          target.value = ''
          resumeForm.value.resume = null
          resumeForm.value.resumeFile = ''
        } finally {
          uploading.value = false
        }
      }
    }

    const submitResume = async () => {
      if (submitting.value || uploading.value) return
      
      // 如果有文件但还没上传完成
      if (resumeForm.value.resume && !resumeForm.value.resumeFile) {
        alert('请等待文件上传完成')
        return
      }
      
      submitting.value = true
      
      try {
        const submitData = {
          name: resumeForm.value.name,
          phone: resumeForm.value.phone,
          email: resumeForm.value.email,
          age: resumeForm.value.age,
          jobId: resumeForm.value.jobId,
          experience: resumeForm.value.experience,
          coverLetter: resumeForm.value.coverLetter,
          resumeFile: resumeForm.value.resumeFile
        }

        const response = await fetch('http://localhost:1680/api/resume-submissions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(submitData)
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        
        if (result.success && result.code === 200) {
          alert('简历提交成功！我们会尽快与您联系。')
          closeModal()
        } else {
          throw new Error(result.message || '简历提交失败')
        }
      } catch (err) {
        console.error('简历提交失败:', err)
        alert(err instanceof Error ? err.message : '简历提交失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }

    onMounted(() => {
      fetchJobs()
    })

    return {
      benefits,
      positions,
      loading,
      error,
      showModal,
      resumeForm,
      submitting,
      uploading,
      parseRequirements,
      showResumeForm,
      closeModal,
      handleFileUpload,
      submitResume
    }
  }
})
</script>

<style scoped>
.careers {
  padding-top: 80px;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f1e8 50%, #ede7d3 100%);
  min-height: 100vh;
}

.careers-hero {
  min-height: 60vh;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 80px 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.floating-card:nth-child(1) {
  top: 20%;
  left: 10%;
}

.floating-card:nth-child(2) {
  top: 60%;
  right: 15%;
}

.floating-card:nth-child(3) {
  top: 40%;
  left: 80%;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 32px;
  color: #8B5A3C;
}

.hero-title {
  margin-bottom: 32px;
}

.title-main {
  display: block;
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -2px;
  margin-bottom: 16px;
}

.title-sub {
  display: block;
  font-size: 1.8rem;
  font-weight: 300;
  color: #8B5A3C;
  opacity: 0.8;
}

.hero-description {
  font-size: 1.2rem;
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
}

.why-join {
  padding: 120px 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: -1px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #8B5A3C;
  opacity: 0.7;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.benefit-card {
  padding: 40px 32px;
  border-radius: 24px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.benefit-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(212, 165, 116, 0.2);
}

.benefit-icon {
  font-size: 3rem;
  margin-bottom: 24px;
}

.benefit-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 16px;
}

.benefit-description {
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.6;
}

.job-positions {
  padding: 120px 0;
  background: rgba(255, 255, 255, 0.3);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(212, 165, 116, 0.2);
  border-top: 4px solid #D4A574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #8B5A3C;
  font-size: 16px;
  opacity: 0.7;
}

.error-container {
  text-align: center;
  padding: 40px;
  border-radius: 20px;
  margin-bottom: 40px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.error-text {
  color: #d32f2f;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.error-subtitle {
  color: #8B5A3C;
  opacity: 0.7;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 80px 40px;
  border-radius: 24px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 24px;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 8px;
}

.empty-subtitle {
  color: #8B5A3C;
  opacity: 0.7;
}

.positions-list {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.position-card {
  padding: 40px;
  border-radius: 24px;
  transition: all 0.3s ease;
}

.position-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(212, 165, 116, 0.15);
}

.position-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.position-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 12px;
}

.position-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.position-meta span {
  padding: 4px 12px;
  background: rgba(212, 165, 116, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: #8B5A3C;
}

.position-status {
  padding: 8px 16px;
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.position-requirements {
  margin-bottom: 32px;
}

.position-requirements h4 {
  color: #8B5A3C;
  margin-bottom: 16px;
  font-weight: 600;
}

.position-requirements ul {
  list-style: none;
  padding: 0;
}

.position-requirements li {
  color: #8B5A3C;
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.position-requirements li::before {
  content: '•';
  color: #D4A574;
  position: absolute;
  left: 0;
}

.btn-apply {
  padding: 12px 32px;
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-apply:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.4);
}

.resume-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 24px;
  padding: 40px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.modal-header h3 {
  color: #8B5A3C;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #8B5A3C;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resume-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #8B5A3C;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 1px solid rgba(139, 90, 60, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  color: #8B5A3C;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #D4A574;
  box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #8B5A3C;
  font-size: 14