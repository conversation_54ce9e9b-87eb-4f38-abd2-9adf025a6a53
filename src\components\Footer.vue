<template>
    <div class="footer-container">
      <div class="footer-divider"></div>
      <div class="footer-bottom-content">
        <div class="copyright-info">
          <p>&copy; 2024 宁波圣芽儿商贸科技有限公司 版权所有</p>
          <p class="beian-info">
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" class="beian-link">
              浙ICP备2025186805号
            </a>
          </p>
        </div>
        <p class="footer-slogan gradient-text-gold">让科技改变生活，让服务更贴心</p>
      </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'AppFooter'
})
</script>

<style scoped>
.footer {
  background: rgba(255, 255, 255, 0.1);
  color: #8B5A3C;
  padding: 80px 0 40px;
  margin-top: 120px;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}


.footer-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(139, 90, 60, 0.3), transparent);
  margin-bottom: 40px;
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
  color: #8B5A3C;
  opacity: 0.7;
}

.copyright-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: center;
}

.beian-info {
  font-size: 14px;
  opacity: 0.8;
}

.beian-link {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

.beian-link:hover {
  color: #D4A574;
  text-decoration: underline;
}

.footer-slogan {
  font-weight: 500;
}

@media screen and (max-width: 768px) {
  .footer-container {
    padding: 0 20px;
  }
}
</style>