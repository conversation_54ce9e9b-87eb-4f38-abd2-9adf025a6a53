<template>
  <div class="home">
    <!-- 英雄区域 -->
    <section class="hero" id="home">
      <div class="hero-background">
        <div class="floating-elements">
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 0s;">
            <div class="card-icon">⚡</div>
          </div>
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 1s;">
            <div class="card-icon">🔮</div>
          </div>
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 2s;">
            <div class="card-icon">🚀</div>
          </div>
          <div class="floating-card glass float-tech tech-glow" style="animation-delay: 3s;">
            <div class="card-icon">💎</div>
          </div>
        </div>
        <div class="hero-pattern"></div>
      </div>
      
      <div class="hero-content">
        <div class="hero-text">
          <div class="hero-badge glass tech-glow scan-effect">
            <span class="badge-icon">⚡</span>
            <span class="badge-text gradient-text-cyber">宁波领先的本地生活服务平台</span>
          </div>
          
          <h1 class="hero-title">
            <span class="title-main gradient-text">宁波圣芽儿商贸科技有限公司</span>
            <span class="title-sub gradient-text-gold">智慧生活 · 科技未来</span>
          </h1>
          
          <p class="hero-description">
            我们运用人工智能、大数据、云计算等前沿科技，为宁波市民构建智慧生活生态圈。
            让科技赋能生活，让数据驱动服务，开启本地生活服务的数字化新纪元。
          </p>
          
          <div class="hero-buttons">
            <button class="btn-primary glass cyber-border tech-glow" @click="scrollToSection('products')">
              <span class="btn-text">🚀 探索产品</span>
              <div class="btn-glow"></div>
            </button>
            <button class="btn-secondary glass scan-effect" @click="scrollToSection('contact')">
              <span class="btn-text">💎 商务咨询</span>
              <div class="btn-arrow">→</div>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心优势 -->
    <section class="advantages" id="about">
      <div class="container">
        <div class="section-header">
          <div class="section-badge glass">
            <span class="badge-icon">✨</span>
            <span class="badge-text">核心优势</span>
          </div>
          <h2 class="section-title gradient-text">为什么选择我们</h2>
          <p class="section-subtitle">专业团队 · 创新技术 · 贴心服务</p>
        </div>
        
        <div class="advantages-grid">
          <div class="advantage-card glass" v-for="(advantage, index) in advantages" :key="index">
            <div class="card-header">
              <div class="advantage-icon">
                <div class="icon-bg"></div>
                <span class="icon-emoji">{{ advantage.icon }}</span>
              </div>
              <div class="advantage-number">0{{ index + 1 }}</div>
            </div>
            <div class="card-content">
              <h3 class="advantage-title">{{ advantage.title }}</h3>
              <p class="advantage-description">{{ advantage.description }}</p>
              <div class="advantage-features">
                <div class="feature-item" v-for="feature in advantage.features" :key="feature">
                  <span class="feature-dot"></span>
                  <span class="feature-text">{{ feature }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品展示 -->
    <section class="products" id="products">
      <div class="container">
        <div class="section-header">
          <div class="section-badge glass">
            <span class="badge-icon">🚀</span>
            <span class="badge-text">核心产品</span>
          </div>
          <h2 class="section-title gradient-text">精品产品展示</h2>
          <p class="section-subtitle">优质产品 · 精致服务 · 超值体验</p>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载产品数据...</p>
        </div>
        
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container glass">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <p class="error-subtitle">正在展示默认产品数据</p>
        </div>
        
        <!-- 产品展示 -->
        <div v-else class="products-showcase">
          <div class="product-card glass" v-for="product in products" :key="product.id">
            <div class="product-image-container">
              <div class="product-image">
                <img 
                  :src="`http://localhost:1680${product.mainImage}`" 
                  :alt="product.name"
                  @error="handleImageError"
                  class="product-main-image"
                />
                <div class="image-overlay">
                  <div class="overlay-content">
                    <div class="product-tags">
                      <span 
                        v-for="tag in product.tagList" 
                        :key="tag" 
                        class="product-tag"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 价格标签 -->
              <div class="price-badge" v-if="product.price > 0">
                <div class="price-content">
                  <div v-if="product.hasDiscount" class="discount-price">
                    <span class="current-price">¥{{ product.discountPrice }}</span>
                    <span class="original-price">¥{{ product.price }}</span>
                    <span class="discount-rate">{{ Math.round((1 - product.discountRate) * 100) }}% OFF</span>
                  </div>
                  <div v-else class="regular-price">
                    <span class="price">¥{{ product.price }}</span>
                    <span class="unit">{{ product.priceUnit }}</span>
                  </div>
                </div>
              </div>
              
              <div v-else class="free-badge">
                <span class="free-text">免费体验</span>
              </div>
            </div>
            
            <div class="product-content">
              <div class="product-header">
                <h3 class="product-name">{{ product.name }}</h3>
                <div class="product-category">{{ product.tags }}</div>
              </div>
              
              <p class="product-description">{{ product.description }}</p>
              
              <div class="product-sell-point">
                <div class="sell-point-icon">💎</div>
                <span class="sell-point-text">{{ product.sellPoint }}</span>
              </div>
              
              <div class="product-actions">
                <button class="btn-primary-product glass" @click="scrollToSection('contact')">
                  <span class="btn-text">了解详细</span>
                  <div class="btn-shine"></div>
                </button>
                <button class="btn-secondary-product glass" @click="showProductDetail(product)">
                  <span class="btn-text">查看详情</span>
                  <span class="btn-arrow">→</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!loading && !error && products.length === 0" class="empty-state glass">
          <div class="empty-icon">📦</div>
          <p class="empty-text">暂无产品数据</p>
          <p class="empty-subtitle">请稍后再试</p>
        </div>
      </div>
    </section>

    <!-- 产品详情弹窗 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content glass" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">{{ selectedProduct?.name }}</h3>
          <button class="modal-close" @click="closeModal">×</button>
        </div>
        
        <div class="modal-body" v-if="selectedProduct">
          <div class="product-detail-grid">
            <div class="product-images">
              <div class="main-image-container">
                <img 
                  :src="`http://localhost:1680${selectedProduct.mainImage}`" 
                  :alt="selectedProduct.name"
                  class="modal-main-image"
                />
              </div>
              
              <div class="image-gallery" v-if="selectedProduct.imageList.length > 0">
                <div class="gallery-title">产品图片</div>
                <div class="gallery-grid">
                  <img 
                    v-for="(image, index) in selectedProduct.imageList" 
                    :key="index"
                    :src="`http://localhost:1680${image}`" 
                    :alt="`${selectedProduct.name} - 图片${index + 1}`"
                    class="gallery-image"
                  />
                </div>
              </div>
            </div>
            
            <div class="product-info-detail">
              <div class="info-section">
                <div class="info-label">产品分类</div>
                <div class="info-value">{{ selectedProduct.tags }}</div>
              </div>
              
              <div class="info-section">
                <div class="info-label">产品描述</div>
                <div class="info-value">{{ selectedProduct.description }}</div>
              </div>
              
              <div class="info-section">
                <div class="info-label">产品卖点</div>
                <div class="info-value sell-point">{{ selectedProduct.sellPoint }}</div>
              </div>
              
              <div class="info-section">
                <div class="info-label">标签</div>
                <div class="info-value">
                  <div class="tag-list">
                    <span 
                      v-for="tag in selectedProduct.tagList" 
                      :key="tag" 
                      class="detail-tag"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="info-section price-section">
                <div class="info-label">价格信息</div>
                <div class="price-info">
                  <div v-if="selectedProduct.hasDiscount" class="discount-info">
                    <div class="current-price-large">¥{{ selectedProduct.discountPrice }}</div>
                    <div class="original-price-large">原价: ¥{{ selectedProduct.price }}</div>
                    <div class="discount-badge">{{ Math.round((1 - selectedProduct.discountRate) * 100) }}% OFF</div>
                  </div>
                  <div v-else-if="selectedProduct.price > 0" class="regular-price-large">
                    ¥{{ selectedProduct.price }} {{ selectedProduct.priceUnit }}
                  </div>
                  <div v-else class="free-price">
                    <span class="free-label">免费体验</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button class="btn-modal-primary" @click="scrollToSection('contact'); closeModal()">
            立即咨询
          </button>
          <button class="btn-modal-secondary" @click="closeModal">
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 企业文化 -->
    <section class="culture" id="culture">
      <div class="container">
        <div class="culture-content">
          <div class="section-header">
            <div class="section-badge glass">
              <span class="badge-icon">🎯</span>
              <span class="badge-text">企业文化</span>
            </div>
            <h2 class="section-title gradient-text">我们的价值观</h2>
            <p class="section-subtitle">用心服务 · 创新驱动 · 共同成长</p>
          </div>
          
          <div class="mission-card glass">
            <div class="mission-icon">🌟</div>
            <div class="mission-content">
              <h4 class="mission-title">我们的使命</h4>
              <p class="mission-text">
                通过科技创新和精致服务，让宁波市民享受更便捷、更优质的本地生活体验，
                成为连接用户与美好生活的桥梁。
              </p>
            </div>
          </div>
          
          <div class="values-grid">
            <div class="value-card glass" v-for="(value, index) in values" :key="index">
              <div class="value-icon">{{ value.icon }}</div>
              <div class="value-content">
                <h4 class="value-title">{{ value.title }}</h4>
                <p class="value-description">{{ value.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta" id="contact">
      <div class="container">
        <div class="cta-content glass">
          <div class="cta-header">
            <div class="cta-icon animate-pulse-soft">
              <span class="icon-emoji">🚀</span>
              <div class="icon-rings">
                <div class="ring ring-1"></div>
                <div class="ring ring-2"></div>
                <div class="ring ring-3"></div>
              </div>
            </div>
            <h2 class="cta-title">准备开启数字化转型之旅？</h2>
            <p class="cta-description">
              让我们一起打造属于宁波的智慧生活新体验，为您的业务插上科技的翅膀
            </p>
          </div>
<!--           
          <div class="cta-actions">
            <button class="btn-cta glass">
              <span class="btn-text">立即咨询</span>
              <div class="btn-glow"></div>
            </button>
            <button class="btn-demo-cta glass">
              <span class="btn-text">预约演示</span>
              <span class="btn-arrow">→</span>
            </button>
          </div> -->
          
          <div class="contact-info">
            <div class="contact-item">
              <div class="contact-icon">📞</div>
              <div class="contact-text">19560465815</div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">📧</div>
              <div class="contact-text"><EMAIL></div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">📍</div>
              <div class="contact-text">宁波鄞州区城南商务大厦A座A幢2201</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'

interface Product {
  id: number
  name: string
  tags: string
  description: string
  sellPoint: string
  mainImage: string
  imageGroup: string
  price: number
  priceUnit: string
  discountPrice: number
  discountUnit: string
  tagList: string[]
  imageList: string[]
  hasDiscount: boolean
  discountRate: number
}

interface ApiResponse {
  code: number
  message: string
  data: Product[]
  timestamp: number
  pageNum: number
  pageSize: number
  total: number
  pages: number
  hasNext: boolean
  hasPrevious: boolean
  success: boolean
}

export default defineComponent({
  name: 'HomeView',
  setup() {
    const heroStats = ref([
      { number: '50万+', label: '活跃用户', icon: '👥' },
      { number: '1000+', label: '合作商家', icon: '🏪' },
      { number: '99.9%', label: '服务满意度', icon: '⭐' }
    ])

    const services = ref([
      { icon: '🍜', name: '美食外卖' },
      { icon: '🛒', name: '生鲜购物' },
      { icon: '🚗', name: '出行服务' },
      { icon: '🏠', name: '家政服务' }
    ])

    const advantages = ref([
      {
        icon: '🏠',
        title: '本土基因',
        description: '扎根宁波市场，深谙本地商业逻辑',
        features: ['区域洞察深', '落地无壁垒']
      },
      {
        icon: '⚡',
        title: '实战沉淀',
        description: '源自门店运营，解决真实业务痛点',
        features: ['功能源于实践', '痛点精准击破', '效率直接提升']
      },
      {
        icon: '🔧',
        title: '技术自主',
        description: '自研系统架构，灵活响应定制需求',
        features: ['全自主研发', '迭代响应快', '适配性极强', '低成本升级']
      },
      {
        icon: '🛠️',
        title: '全链服务',
        description: '从上线到运维，提供一站式技术支持',
        features: ['上门实施上线', '定制化培训', '本地化售后']
      },
      {
        icon: '🏆',
        title: '标杆认证',
        description: '服务京东体系，彰显专业服务能力',
        features: ['数据成果显著', '方案成熟可靠']
      }
    ])

    const products = ref<Product[]>([])
    const loading = ref(false)
    const error = ref('')
    const showModal = ref(false)
    const selectedProduct = ref<Product | null>(null)

    const values = ref([
      {
        icon: '💡',
        title: '创新驱动',
        description: '持续探索新技术，用创新思维解决实际问题，为用户创造价值'
      },
      {
        icon: '🤝',
        title: '合作共赢',
        description: '与合作伙伴携手共进，构建良性生态，实现多方共赢'
      },
      {
        icon: '🎯',
        title: '用户至上',
        description: '始终以用户需求为中心，提供超越期待的产品和服务体验'
      },
      {
        icon: '📈',
        title: '追求卓越',
        description: '不断提升专业能力，追求产品和服务的卓越品质'
      }
    ])

    // 获取产品数据
    const fetchProducts = async () => {
      loading.value = true
      error.value = ''
      
      try {
        const response = await fetch('http://localhost:1680/api/products')
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const result: ApiResponse = await response.json()
        
        if (result.success && result.code === 200) {
          products.value = result.data
        } else {
          throw new Error(result.message || '获取产品数据失败')
        }
      } catch (err) {
        console.error('获取产品数据失败:', err)
        error.value = err instanceof Error ? err.message : '网络请求失败'
        
        // 如果API请求失败，使用默认数据
        products.value = [
          {
            id: 1,
            name: '宁波生活APP',
            tags: '移动应用',
            description: '集成本地生活服务的一站式移动应用平台，为用户提供便捷的生活服务体验',
            sellPoint: '一键下单，实时配送，智能推荐',
            mainImage: '/api/files/product/default_app.jpg',
            imageGroup: '[]',
            price: 0,
            priceUnit: '免费',
            discountPrice: 0,
            discountUnit: '免费',
            tagList: ['移动应用', '本地生活'],
            imageList: [],
            hasDiscount: false,
            discountRate: 0
          },
          {
            id: 2,
            name: '商家管理系统',
            tags: 'SaaS平台',
            description: '为本地商家提供全方位的数字化管理解决方案，助力商家数字化转型',
            sellPoint: '订单管理，数据分析，营销工具',
            mainImage: '/api/files/product/default_saas.jpg',
            imageGroup: '[]',
            price: 999,
            priceUnit: '元/月',
            discountPrice: 799,
            discountUnit: '元/月',
            tagList: ['SaaS平台', '数字化管理'],
            imageList: [],
            hasDiscount: true,
            discountRate: 0.8
          }
        ]
      } finally {
        loading.value = false
      }
    }

    const scrollToSection = (sectionId: string) => {
      const element = document.getElementById(sectionId)
      if (element) {
        const offsetTop = element.offsetTop - 80
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        })
      }
    }

    const showProductDetail = (product: Product) => {
      selectedProduct.value = product
      showModal.value = true
      document.body.style.overflow = 'hidden'
    }

    const closeModal = () => {
      showModal.value = false
      selectedProduct.value = null
      document.body.style.overflow = 'auto'
    }

    const handleImageError = (e: Event) => {
      const target = e.target as HTMLImageElement
      if (target) {
        target.src = '/api/files/product/default.jpg'
      }
    }

    onMounted(() => {
      // 获取产品数据
      fetchProducts()
      
      setTimeout(() => {
        try {
          const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
          }

          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting && entry.target) {
                entry.target.classList.add('animate-in')
              }
            })
          }, observerOptions)

          const elements = document.querySelectorAll('.advantage-card, .product-card, .value-card')
          if (elements && elements.length > 0) {
            elements.forEach(el => {
              if (el && typeof el.getBoundingClientRect === 'function') {
                observer.observe(el)
              }
            })
          }
        } catch (error) {
          console.log('Animation observer error:', error)
        }
      }, 500)
    })

    return {
      services,
      advantages,
      products,
      values,
      loading,
      error,
      showModal,
      selectedProduct,
      scrollToSection,
      showProductDetail,
      closeModal,
      handleImageError
    }
  }
})
</script>

<style scoped>
.home {
  padding-top: 80px;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0e4b99 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.home::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px),
    linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 1;
}

/* 英雄区域 */
.hero {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 120px 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  z-index: 2;
}

.floating-card .card-icon {
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
}

.floating-card:nth-child(1) {
  top: 20%;
  left: 10%;
}

.floating-card:nth-child(2) {
  top: 60%;
  right: 15%;
}

.floating-card:nth-child(3) {
  top: 40%;
  left: 80%;
}

.floating-card:nth-child(4) {
  top: 70%;
  left: 70%;
}

.hero-pattern {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(212, 165, 116, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 90, 60, 0.1) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: float 20s ease-in-out infinite;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 3;
}

.hero-text {
  text-align: center;
  max-width: 800px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 32px;
  color: #ffffff;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
}

.hero-badge .badge-icon {
  filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.8));
}

.badge-icon {
  font-size: 16px;
}

.hero-title {
  margin-bottom: 32px;
}

.title-main {
  display: block;
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -2px;
  margin-bottom: 16px;
  white-space: nowrap;
  min-width: max-content;
}

.title-sub {
  display: block;
  font-size: 2rem;
  font-weight: 300;
  color: #ffffff;
  opacity: 0.9;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.hero-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.9;
  line-height: 1.7;
  margin-bottom: 48px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-buttons {
  display: flex;
  gap: 20px;
  margin-bottom: 60px;
  justify-content: center;
}

.btn-primary, .btn-secondary {
  position: relative;
  padding: 16px 32px;
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  color: white;
  border: 1px solid rgba(0, 212, 255, 0.5);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 212, 255, 0.6), 0 0 30px rgba(124, 58, 237, 0.4);
  background: linear-gradient(135deg, #7c3aed 0%, #00d4ff 100%);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
}

.btn-secondary:hover {
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 212, 255, 0.3);
  color: #00d4ff;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-primary:hover .btn-glow {
  left: 100%;
}

/* 核心优势 */
.advantages {
  padding: 120px 0;
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 24px;
  color: #8B5A3C;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: -1px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #8B5A3C;
  opacity: 0.7;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.advantage-card {
  padding: 40px 32px;
  border-radius: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(30px);
}

.advantage-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.advantage-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(212, 165, 116, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.advantage-icon {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.icon-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  border-radius: 16px;
  opacity: 0.1;
}

.icon-emoji {
  font-size: 1.8rem;
  position: relative;
  z-index: 2;
}

.advantage-number {
  font-size: 2rem;
  font-weight: 700;
  color: #D4A574;
  opacity: 0.3;
}

.advantage-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 16px;
}

.advantage-description {
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.6;
  margin-bottom: 24px;
}

.advantage-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-dot {
  width: 6px;
  height: 6px;
  background: #D4A574;
  border-radius: 50%;
}

.feature-text {
  font-size: 14px;
  color: #8B5A3C;
  opacity: 0.8;
}

/* 产品展示 */
.products {
  padding: 120px 0;
  background: rgba(255, 255, 255, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(212, 165, 116, 0.2);
  border-top: 4px solid #D4A574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #8B5A3C;
  font-size: 16px;
  opacity: 0.7;
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 40px;
  border-radius: 20px;
  margin-bottom: 40px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.error-text {
  color: #d32f2f;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.error-subtitle {
  color: #8B5A3C;
  opacity: 0.7;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  border-radius: 24px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 24px;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 8px;
}

.empty-subtitle {
  color: #8B5A3C;
  opacity: 0.7;
}

/* 产品展示网格 */
.products-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  margin-top: 40px;
}

.product-card {
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  transform: translateY(30px);
}

.product-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(212, 165, 116, 0.25);
}

/* 产品图片容器 */
.product-image-container {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.product-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.product-card:hover .product-main-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .image-overlay {
  opacity: 1;
}

.overlay-content {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.product-tag {
  background: rgba(255, 255, 255, 0.9);
  color: #8B5A3C;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 价格标签 */
.price-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.3);
}

.price-content {
  text-align: center;
}

.discount-price {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
}

.original-price {
  font-size: 12px;
  text-decoration: line-through;
  opacity: 0.7;
}

.discount-rate {
  font-size: 10px;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 600;
}

.regular-price {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price {
  font-size: 18px;
  font-weight: 700;
}

.unit {
  font-size: 12px;
  opacity: 0.8;
}

.free-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.free-text {
  font-size: 12px;
  font-weight: 600;
}

/* 产品内容 */
.product-content {
  padding: 32px 24px;
}

.product-header {
  margin-bottom: 16px;
}

.product-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #8B5A3C;
  margin-bottom: 8px;
}

.product-category {
  display: inline-block;
  background: rgba(212, 165, 116, 0.1);
  color: #D4A574;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.product-description {
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 14px;
}

.product-sell-point {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 12px 16px;
  background: rgba(212, 165, 116, 0.05);
  border-radius: 12px;
  border-left: 3px solid #D4A574;
}

.sell-point-icon {
  font-size: 16px;
}

.sell-point-text {
  color: #8B5A3C;
  font-size: 14px;
  font-weight: 500;
}

.product-actions {
  display: flex;
  gap: 12px;
}

.btn-primary-product, .btn-secondary-product {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.btn-primary-product {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
}

.btn-primary-product:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.4);
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-primary-product:hover .btn-shine {
  left: 100%;
}

.btn-secondary-product {
  color: #8B5A3C;
  border: 1px solid rgba(139, 90, 60, 0.3);
  background: rgba(255, 255, 255, 0.5);
}

.btn-secondary-product:hover {
  color: #D4A574;
  border-color: #D4A574;
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.8);
}

/* 企业文化 */
.culture {
  padding: 120px 0;
}

.culture-content {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.mission-card {
  display: flex;
  gap: 20px;
  padding: 32px;
  border-radius: 20px;
  margin-bottom: 40px;
}

.mission-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.mission-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 12px;
}

.mission-text {
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.6;
}

.values-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.value-card {
  padding: 24px;
  border-radius: 16px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
}

.value-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.value-card:hover {
  transform: translateY(-4px);
}

.value-icon {
  font-size: 1.8rem;
  margin-bottom: 16px;
}

.value-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #8B5A3C;
  margin-bottom: 8px;
}

.value-description {
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.5;
  font-size: 14px;
}

/* CTA区域 */
.cta {
  padding: 120px 0;
  background: rgba(255, 255, 255, 0.5);
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 80px 60px;
  border-radius: 32px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-header {
  margin-bottom: 48px;
}

.cta-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  margin-bottom: 32px;
}

.icon-rings {
  position: absolute;
  inset: 0;
}

.ring {
  position: absolute;
  border: 2px solid rgba(212, 165, 116, 0.3);
  border-radius: 50%;
  animation: pulse-soft 3s ease-in-out infinite;
}

.ring-1 {
  inset: 0;
  animation-delay: 0s;
}

.ring-2 {
  inset: -10px;
  animation-delay: 1s;
}

.ring-3 {
  inset: -20px;
  animation-delay: 2s;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #8B5A3C;
  margin-bottom: 20px;
}

.cta-description {
  font-size: 1.2rem;
  color: #8B5A3C;
  opacity: 0.7;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 48px;
}

.btn-cta, .btn-demo-cta {
  padding: 16px 32px;
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-cta {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
}

.btn-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(212, 165, 116, 0.4);
}

.btn-demo-cta {
  color: #8B5A3C;
  border: 1px solid rgba(139, 90, 60, 0.3);
}

.btn-demo-cta:hover {
  color: #D4A574;
  border-color: #D4A574;
  transform: translateY(-1px);
}

.contact-info {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #8B5A3C;
  opacity: 0.7;
}

.contact-icon {
  font-size: 1.2rem;
}

.contact-text {
  font-size: 14px;
}

/* 产品详情弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(139, 90, 60, 0.1);
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #8B5A3C;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: #8B5A3C;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(139, 90, 60, 0.1);
  transform: rotate(90deg);
}

.modal-body {
  padding: 32px;
}

.product-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-image-container {
  width: 100%;
  height: 300px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.modal-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gallery-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #8B5A3C;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
}

.gallery-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gallery-image:hover {
  transform: scale(1.05);
}

.product-info-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #8B5A3C;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #8B5A3C;
  line-height: 1.6;
}

.info-value.sell-point {
  background: rgba(212, 165, 116, 0.1);
  padding: 12px 16px;
  border-radius: 12px;
  border-left: 3px solid #D4A574;
  font-weight: 500;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.detail-tag {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.price-section {
  background: rgba(212, 165, 116, 0.05);
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(212, 165, 116, 0.2);
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.current-price-large {
  font-size: 2rem;
  font-weight: 700;
  color: #D4A574;
}

.original-price-large {
  font-size: 1rem;
  color: #8B5A3C;
  opacity: 0.6;
  text-decoration: line-through;
}

.discount-badge {
  display: inline-block;
  background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  width: fit-content;
}

.regular-price-large {
  font-size: 2rem;
  font-weight: 700;
  color: #D4A574;
}

.free-price {
  display: flex;
  align-items: center;
}

.free-label {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 600;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid rgba(139, 90, 60, 0.1);
}

.btn-modal-primary, .btn-modal-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-modal-primary {
  background: linear-gradient(135deg, #D4A574 0%, #8B5A3C 100%);
  color: white;
}

.btn-modal-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.4);
}

.btn-modal-secondary {
  background: rgba(139, 90, 60, 0.1);
  color: #8B5A3C;
  border: 1px solid rgba(139, 90, 60, 0.3);
}

.btn-modal-secondary:hover {
  background: rgba(139, 90, 60, 0.2);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .container {
    padding: 0 30px;
  }
  
  .hero-content {
    padding: 0 30px;
  }
  
  .products-showcase {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media screen and (max-width: 768px) {
  .hero-content {
    text-align: center;
  }
  
  .title-main {
    font-size: 2.8rem;
    white-space: normal;
  }
  
  .title-sub {
    font-size: 1.6rem;
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
  }
  
  .products-showcase {
    grid-template-columns: 1fr;
  }
  
  .values-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-content {
    padding: 60px 40px;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .contact-info {
    flex-direction: column;
    gap: 20px;
  }
  
  .section-title {
    font-size: 2.2rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .product-actions {
    flex-direction: column;
  }
}

@media screen and (max-width: 480px) {
  .container {
    padding: 0 20px;
  }
  
  .hero-content {
    padding: 0 20px;
  }
  
  .title-main {
    font-size: 2.2rem;
  }
  
  .title-sub {
    font-size: 1.4rem;
  }
  
  .cta-content {
    padding: 40px 20px;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  /* 弹窗响应式 */
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-title {
    font-size: 1.4rem;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .product-detail-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .main-image-container {
    height: 200px;
  }
  
  .modal-footer {
    padding: 16px 20px;
    flex-direction: column;
  }
  
  .btn-modal-primary, .btn-modal-secondary {
    width: 100%;
  }
}

/* 中等屏幕弹窗响应式 */
@media screen and (max-width: 768px) {
  .product-detail-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .main-image-container {
    height: 250px;
  }
  
  .modal-header {
    padding: 20px 24px;
  }
  
  .modal-body {
    padding: 24px;
  }
  
  .modal-footer {
    padding: 20px 24px;
  }
}
</style>
