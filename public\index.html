<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta name="description" content="宁波圣芽儿商贸科技有限公司 - 运用AI、大数据、云计算等前沿科技，构建智慧生活生态圈，开启本地生活服务的数字化新纪元">
    <meta name="keywords" content="宁波科技公司,本地生活服务,智慧生活,人工智能,大数据,云计算,数字化转型">
    <meta name="theme-color" content="#00d4ff">
    <link rel="icon" href="./favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <title>宁波圣芽儿商贸科技有限公司 - 智慧生活·科技未来</title>
    <style>
      /* 预加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0e4b99 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 3px solid rgba(0, 212, 255, 0.3);
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        position: absolute;
        margin-top: 100px;
        color: #00d4ff;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        letter-spacing: 2px;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>

    <!-- 预加载动画 -->
    <div id="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">LOADING...</div>
    </div>

    <div id="app"></div>

    <!-- 隐藏加载动画的脚本 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>

    <!-- built files will be auto injected -->
  </body>
</html>
